{"name": "gemini-agent", "version": "0.1.0", "description": "An autonomous agent powered by Google Gemini AI", "main": "agent.js", "bin": {"gemini-agent": "agent.js"}, "scripts": {"start": "node agent.js", "interactive": "node agent.js interactive"}, "keywords": ["ai", "cli", "coding", "assistant", "web-research", "automation"], "author": "", "license": "MIT", "type": "commonjs", "dependencies": {"@google/generative-ai": "^0.1.3", "axios": "^1.8.4", "body-parser": "^2.2.0", "chalk": "^4.1.2", "cheerio": "^1.0.0", "commander": "^11.1.0", "express": "^4.21.2", "glob": "^8.1.0", "http-server": "^14.1.1", "jsdom": "^26.1.0", "open": "^10.1.1", "ora": "^5.4.1", "puppeteer": "^24.7.2", "terminal-link": "^4.0.0", "util": "^0.12.5", "uuid": "^9.0.1", "ws": "^8.18.1"}, "engines": {"node": ">=14.0.0"}}