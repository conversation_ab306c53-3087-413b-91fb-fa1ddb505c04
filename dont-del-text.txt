#!/usr/bin/env node
/**
 * SHAI - Smart AI Agent v4.5 
 * Ultra-powerful streaming AI assistant with 50+ tools and advanced reasoning
 * 
 * Features:
 * - Real-time streaming with chain-of-thought reasoning
 * - 50+ integrated tools for complete development workflow  
 * - Advanced memory system with persistent context
 * - Smart tool orchestration and auto-invocation
 * - Beautiful OpenAI Codex-style CLI interface
 * - Intelligent task planning and execution
 * 
 * @version 4.5.0
 * @license MIT
 */

const { program } = require('commander');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const axios = require('axios');
const { exec, spawn } = require('child_process');
const readline = require('readline');
const os = require('os');
const { promisify } = require('util');
const execAsync = promisify(exec);
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const cheerio = require('cheerio');
const { JSDOM } = require('jsdom');

// ═══════════════════════════════════════════════════════════════════════════════
// 🎨 BEAUTIFUL CLI INTERFACE COMPONENTS
// ═══════════════════════════════════════════════════════════════════════════════

const UI = {
  colors: {
    primary: chalk.hex('#00D4AA'),
    secondary: chalk.hex('#FF6B6B'), 
    success: chalk.hex('#4ECDC4'),
    warning: chalk.hex('#FFE66D'),
    error: chalk.hex('#FF6B6B'),
    muted: chalk.hex('#95A5A6'),
    dimmed: chalk.hex('#BDC3C7').dim,
    code: chalk.hex('#A8E6CF'),
    thinking: chalk.hex('#F39C12').dim,
    tool: chalk.hex('#3498DB'),
    highlight: chalk.hex('#E74C3C').bold,
    accent: chalk.hex('#9B59B6')
  },

  symbols: {
    robot: '🤖',
    thinking: '🧠',
    tool: '⚡',
    success: '✅',
    error: '❌',
    arrow: '➤',
    bullet: '●',
    lightning: '⚡',
    brain: '🧠',
    magic: '✨',
    gear: '⚙️',
    search: '🔍',
    memory: '💾',
    chain: '🔗'
  },

  banner() {
    console.clear();
    console.log(this.colors.primary.bold(`
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║  ${this.symbols.robot} ${this.colors.highlight('SHAI')} ${this.colors.primary('- Smart AI Agent v4.0')} ${this.symbols.lightning}               ║
    ║                                                              ║
    ║  ${this.colors.dimmed('Powered by Gemini 2.0 Flash • 40+ Tools • Advanced Memory')}  ║
    ║  ${this.colors.dimmed('Chain-of-Thought • Sub-Agents • Real-time Streaming')}        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    `));
  },

  prompt() {
    return `\n${this.colors.primary('┌─')} ${this.colors.secondary.bold('You')} ${this.colors.primary('─')} `;
  },

  thinking(message, type = 'general') {
    const typeSymbols = {
      general: this.symbols.thinking,
      planning: this.symbols.gear,
      analyzing: this.symbols.search,
      deciding: this.symbols.brain
    };
    return `${this.colors.thinking('├─')} ${typeSymbols[type] || this.symbols.thinking} ${this.colors.thinking('Thinking')} ${this.colors.dimmed('─')} ${this.colors.dimmed(message)}`;
  },

  chainOfThought(step, message) {
    return `${this.colors.thinking('│ ')} ${this.colors.dimmed(`${step}. ${message}`)}`;
  },

  toolCall(toolName, params) {
    const paramsStr = Object.keys(params).length > 0 ? 
      ` ${this.colors.muted(JSON.stringify(params, null, 0).substring(0, 60))}` : '';
    return `${this.colors.tool('├─')} ${this.symbols.tool} ${this.colors.tool.bold(toolName)}${paramsStr}`;
  },

  toolResult(success, message) {
    const symbol = success ? this.symbols.success : this.symbols.error;
    const color = success ? this.colors.success : this.colors.error;
    return `${this.colors.tool('│ ')} ${symbol} ${color(message.substring(0, 80))}${message.length > 80 ? '...' : ''}`;
  },

  response() {
    return `${this.colors.primary('└─')} ${this.symbols.robot} ${this.colors.highlight('SHAI')} ${this.colors.primary('─')} `;
  },

  success(message) {
    return `${this.colors.success('└─')} ${this.symbols.success} ${message}`;
  },

  error(message) {
    return `${this.colors.error('└─')} ${this.symbols.error} ${message}`;
  },

  section(title) {
    return `\n${this.colors.primary('│')} ${this.colors.secondary.bold(title)}\n${this.colors.primary('│')}`;
  },

  subAgent(agentName, task) {
    return `${this.colors.accent('├─')} ${this.symbols.chain} ${this.colors.accent('Sub-Agent')} ${this.colors.muted('─')} ${this.colors.code(agentName)} ${this.colors.dimmed(task.substring(0, 60))}`;
  }
};

// ═══════════════════════════════════════════════════════════════════════════════
// ⚙️ CORE CONFIGURATION & INITIALIZATION  
// ═══════════════════════════════════════════════════════════════════════════════

const CONFIG_DIR = path.join(os.homedir(), '.shai-agent');
const MEMORY_FILE = path.join(CONFIG_DIR, 'memory.json');
const CONTEXT_FILE = path.join(CONFIG_DIR, 'context.json');
const CONFIG_FILE = path.join(CONFIG_DIR, 'config.json');

const DEFAULT_CONFIG = {
  apiKey: process.env.GEMINI_API_KEY || 'AIzaSyDOE7pTDMrVPGmNlkuCpgFav-85hjsrTtw',
  model: 'gemini-2.0-flash',
  temperature: 0.7,
  maxOutputTokens: 8192,
  streamingEnabled: true,
  debug: process.env.DEBUG === 'true',
  maxMemorySize: 1000,
  contextWindow: 50
};

let config = { ...DEFAULT_CONFIG };
let genAI = null;
let model = null;
let memory = [];
let context = [];
let subAgents = new Map();

// ═══════════════════════════════════════════════════════════════════════════════
// 💾 ADVANCED MEMORY & CONTEXT MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════

class MemoryManager {
  static async ensureDirectories() {
    try {
      await fs.mkdir(CONFIG_DIR, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  static async loadMemory() {
    try {
      const data = await fs.readFile(MEMORY_FILE, 'utf8');
      memory = JSON.parse(data);
    } catch (error) {
      memory = [];
    }
  }

  static async saveMemory() {
    try {
      await fs.writeFile(MEMORY_FILE, JSON.stringify(memory, null, 2));
    } catch (error) {
      if (config.debug) console.error('Failed to save memory:', error.message);
    }
  }

  static async loadContext() {
    try {
      const data = await fs.readFile(CONTEXT_FILE, 'utf8');
      context = JSON.parse(data);
    } catch (error) {
      context = [];
    }
  }

  static async saveContext() {
    try {
      await fs.writeFile(CONTEXT_FILE, JSON.stringify(context.slice(-config.contextWindow), null, 2));
    } catch (error) {
      if (config.debug) console.error('Failed to save context:', error.message);
    }
  }

  static addMemory(type, content, metadata = {}) {
    const memoryItem = {
      id: uuidv4(),
      type,
      content,
      metadata,
      timestamp: Date.now(),
      importance: metadata.importance || 1
    };
    
    memory.push(memoryItem);
    
    // Keep memory size manageable
    if (memory.length > config.maxMemorySize) {
      memory.sort((a, b) => b.importance - a.importance);
      memory = memory.slice(0, config.maxMemorySize);
    }
    
    this.saveMemory();
    return memoryItem.id;
  }

  static addContext(role, content, metadata = {}) {
    context.push({
      role,
      content,
      metadata,
      timestamp: Date.now()
    });
    
    if (context.length > config.contextWindow) {
      context = context.slice(-config.contextWindow);
    }
    
    this.saveContext();
  }

  static searchMemory(query, limit = 10) {
    if (!query || query.trim() === '') {
      return memory
        .sort((a, b) => b.importance - a.importance)
        .slice(0, limit);
    }
    
    const queryLower = query.toLowerCase();
    return memory
      .filter(item => {
        const content = typeof item.content === 'string' ? item.content : JSON.stringify(item.content);
        const type = typeof item.type === 'string' ? item.type : '';
        const metadata = typeof item.metadata === 'object' ? JSON.stringify(item.metadata) : '';
        
        return content.toLowerCase().includes(queryLower) ||
               type.toLowerCase().includes(queryLower) ||
               metadata.toLowerCase().includes(queryLower);
      })
      .sort((a, b) => b.importance - a.importance)
      .slice(0, limit);
  }

  static getRecentContext(limit = 10) {
    return context.slice(-limit);
  }

  static getContextSummary() {
    const recentContext = this.getRecentContext(20);
    return recentContext.map(ctx => `${ctx.role}: ${typeof ctx.content === 'string' ? ctx.content.substring(0, 100) : JSON.stringify(ctx.content).substring(0, 100)}...`).join('\n');
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🛠️ COMPREHENSIVE TOOL SYSTEM (40+ TOOLS)
// ═══════════════════════════════════════════════════════════════════════════════

class ToolRegistry {
  static tools = new Map();

  static register(name, func, description, schema = {}) {
    this.tools.set(name, {
      func,
      description,
      schema,
      usage: 0,
      lastUsed: null,
      category: schema.category || 'general'
    });
  }

  static async execute(name, params = {}) {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool '${name}' not found`);
    }

    console.log(UI.toolCall(name, params));
    
    tool.usage++;
    tool.lastUsed = Date.now();
    
    try {
      const result = await tool.func(params);
      
      if (result?.success) {
        console.log(UI.toolResult(true, result.message || 'Completed successfully'));
      } else {
        console.log(UI.toolResult(false, result.error || 'Failed'));
      }
      
      // Add to memory if significant
      if (result && typeof result === 'object' && result.significant) {
        MemoryManager.addMemory('tool_result', { tool: name, params, result }, { importance: 2 });
      }
      
      return result;
    } catch (error) {
      console.log(UI.toolResult(false, error.message));
      throw error;
    }
  }

  static list() {
    return Array.from(this.tools.entries()).map(([name, tool]) => ({
      name,
      description: tool.description,
      usage: tool.usage,
      lastUsed: tool.lastUsed,
      category: tool.category
    }));
  }

  static getByCategory(category) {
    return Array.from(this.tools.entries())
      .filter(([_, tool]) => tool.category === category)
      .map(([name, tool]) => ({ name, ...tool }));
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📁 FILE SYSTEM TOOLS
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('create_file', async ({ filePath, content }) => {
  try {
    const dir = path.dirname(filePath);
    await fs.mkdir(dir, { recursive: true });
    await fs.writeFile(filePath, content, 'utf8');
    return { success: true, message: `File created: ${filePath}`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Create a file with specified content', { category: 'filesystem' });

ToolRegistry.register('edit_file', async ({ filePath, changes, lineNumber, insertMode = 'replace' }) => {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    if (insertMode === 'insert') {
      lines.splice(lineNumber, 0, changes);
    } else if (insertMode === 'append') {
      lines.push(changes);
    } else if (insertMode === 'replace') {
      lines[lineNumber] = changes;
    }
    
    await fs.writeFile(filePath, lines.join('\n'), 'utf8');
    return { success: true, message: `File edited: ${filePath}`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Edit a file with specific changes', { category: 'filesystem' });

ToolRegistry.register('edit_single_or_multi_file', async ({ files, operation = 'edit' }) => {
  try {
    const results = [];
    for (const file of files) {
      const result = await ToolRegistry.execute('edit_file', file);
      results.push({ file: file.filePath, result });
    }
    return { success: true, results, message: `Edited ${files.length} files`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Edit single or multiple files in batch', { category: 'filesystem' });

ToolRegistry.register('read_file', async ({ filePath, startLine = 1, endLine = null }) => {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    if (endLine) {
      const selectedLines = lines.slice(startLine - 1, endLine);
      return { success: true, content: selectedLines.join('\n'), lines: selectedLines.length };
    }
    
    return { success: true, content, lines: lines.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Read file content with optional line range', { category: 'filesystem' });

ToolRegistry.register('insert_edit_into_file', async ({ filePath, position, content, method = 'insert' }) => {
  try {
    const fileContent = await fs.readFile(filePath, 'utf8');
    const lines = fileContent.split('\n');
    
    if (method === 'insert') {
      lines.splice(position, 0, content);
    } else if (method === 'replace') {
      lines[position] = content;
    } else if (method === 'after') {
      lines.splice(position + 1, 0, content);
    }
    
    await fs.writeFile(filePath, lines.join('\n'), 'utf8');
    return { success: true, message: `Content inserted into ${filePath}`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Insert code/content at specific location in file', { category: 'filesystem' });

ToolRegistry.register('create_directory', async ({ dirPath, recursive = true }) => {
  try {
    await fs.mkdir(dirPath, { recursive });
    return { success: true, message: `Directory created: ${dirPath}`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Create directories with optional recursive creation', { category: 'filesystem' });

ToolRegistry.register('list_dir', async ({ dirPath = '.', recursive = false, pattern = null }) => {
  try {
    async function scanDirectory(dir, isRecursive = false) {
      const items = await fs.readdir(dir, { withFileTypes: true });
      let results = [];
      
      for (const item of items) {
        const itemPath = path.join(dir, item.name);
        
        if (pattern && !item.name.match(new RegExp(pattern))) {
          continue;
        }
        
        const stats = await fs.stat(itemPath);
        results.push({
          name: item.name,
          path: itemPath,
          type: item.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime
        });
        
        if (isRecursive && item.isDirectory()) {
          const subItems = await scanDirectory(itemPath, true);
          results = results.concat(subItems);
        }
      }
      
      return results;
    }
    
    const items = await scanDirectory(dirPath, recursive);
    return { success: true, items, count: items.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'List directory contents with optional recursion and patterns', { category: 'filesystem' });

ToolRegistry.register('file_search', async ({ pattern, directory = '.', fileTypes = [] }) => {
  try {
    async function searchFiles(dir, searchPattern, types) {
      const items = await fs.readdir(dir, { withFileTypes: true });
      let results = [];
      
      for (const item of items) {
        const itemPath = path.join(dir, item.name);
        
        if (item.isDirectory()) {
          const subResults = await searchFiles(itemPath, searchPattern, types);
          results = results.concat(subResults);
        } else {
          const matchesPattern = item.name.includes(searchPattern) || 
                                item.name.match(new RegExp(searchPattern));
          const matchesType = types.length === 0 || 
                             types.some(type => item.name.endsWith(`.${type}`));
          
          if (matchesPattern && matchesType) {
            const stats = await fs.stat(itemPath);
            results.push({
              path: itemPath,
              name: item.name,
              size: stats.size,
              modified: stats.mtime
            });
          }
        }
      }
      
      return results;
    }
    
    const files = await searchFiles(directory, pattern, fileTypes);
    return { success: true, files, count: files.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Search for files using glob patterns', { category: 'filesystem' });

ToolRegistry.register('grep_search', async ({ pattern, filePath, isRegex = false, caseSensitive = true }) => {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    const flags = caseSensitive ? 'g' : 'gi';
    const searchPattern = isRegex ? new RegExp(pattern, flags) : 
                         new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);
    
    const matches = [];
    lines.forEach((line, index) => {
      if (searchPattern.test(line)) {
        matches.push({
          lineNumber: index + 1,
          content: line,
          match: line.match(searchPattern)
        });
      }
    });
    
    return { success: true, matches, count: matches.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Search for patterns inside files using regex', { category: 'filesystem' });

ToolRegistry.register('list_code_usages', async ({ symbol, directory = '.', fileTypes = ['js', 'ts', 'py', 'go', 'rs'] }) => {
  try {
    const results = [];
    
    // Get all files of specified types
    const { files } = await ToolRegistry.execute('file_search', { 
      pattern: '**/*', 
      directory, 
      fileTypes 
    });
    
    // Search for symbol usage in each file
    for (const file of files) {
      try {
        const { matches } = await ToolRegistry.execute('grep_search', { 
          pattern: symbol, 
          filePath: file.path 
        });
        
        if (matches.length > 0) {
          results.push({
            file: file.path,
            usages: matches
          });
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    return { success: true, symbol, results, totalUsages: results.reduce((sum, r) => sum + r.usages.length, 0) };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Find where symbols (functions/classes) are used', { category: 'filesystem' });

ToolRegistry.register('get_changed_files', async ({ directory = '.' }) => {
  try {
    // Check if git is available
    const gitCheck = await execAsync('git --version', { cwd: directory });
    
    // Get git status
    const { stdout } = await execAsync('git status --porcelain', { cwd: directory });
    const changes = stdout.trim().split('\n').filter(line => line.trim());
    
    // Get diff for each changed file
    const files = [];
    for (const line of changes) {
      if (line.trim()) {
        const status = line.substring(0, 2);
        const filePath = line.substring(3);
        
        try {
          const { stdout: diff } = await execAsync(`git diff HEAD -- "${filePath}"`, { cwd: directory });
          files.push({
            path: filePath,
            status: status.trim(),
            diff: diff
          });
        } catch (error) {
          files.push({
            path: filePath,
            status: status.trim(),
            diff: 'Unable to get diff'
          });
        }
      }
    }
    
    return { success: true, files, count: files.length, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Show Git diffs of modified files', { category: 'filesystem' });

ToolRegistry.register('get_errors', async ({ directory = '.', type = 'all' }) => {
  try {
    const errors = [];
    
    // Check for common error sources
    const errorChecks = {
      eslint: 'npx eslint . --format json',
      tsc: 'npx tsc --noEmit --pretty false',
      python: 'python -m py_compile',
      go: 'go build ./...',
      rust: 'cargo check --message-format json'
    };
    
    if (type === 'all' || type === 'eslint') {
      try {
        await execAsync(errorChecks.eslint, { cwd: directory });
      } catch (error) {
        if (error.stdout) {
          try {
            const eslintResults = JSON.parse(error.stdout);
            eslintResults.forEach(result => {
              result.messages.forEach(msg => {
                errors.push({
                  type: 'eslint',
                  file: result.filePath,
                  line: msg.line,
                  column: msg.column,
                  message: msg.message,
                  severity: msg.severity
                });
              });
            });
          } catch (parseError) {
            // ESLint not available or no config
          }
        }
      }
    }
    
    // Add more error checkers based on project type
    const projectInfo = await ToolRegistry.execute('get_project_setup_info', { directory });
    
    return { success: true, errors, count: errors.length, projectInfo };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Fetch lint, syntax, or compiler errors', { category: 'filesystem' });

// ═══════════════════════════════════════════════════════════════════════════════
// 💻 TERMINAL & SHELL TOOLS
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('run_in_terminal', async ({ command, cwd = process.cwd(), timeout = 30000 }) => {
  try {
    const { stdout, stderr } = await execAsync(command, { cwd, timeout });
    
    MemoryManager.addMemory('command_execution', { command, cwd, stdout, stderr }, { importance: 1 });
    
    return { 
      success: true, 
      stdout: stdout.trim(), 
      stderr: stderr.trim(),
      command,
      cwd,
      significant: stdout.length > 100 || stderr.length > 0
    };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      stdout: error.stdout?.trim() || '',
      stderr: error.stderr?.trim() || '',
      command,
      cwd
    };
  }
}, 'Execute terminal commands with output capture', { category: 'terminal' });

ToolRegistry.register('get_terminal_output', async ({ command, cwd = process.cwd() }) => {
  return await ToolRegistry.execute('run_in_terminal', { command, cwd });
}, 'Capture and analyze output from command', { category: 'terminal' });

ToolRegistry.register('install_python_packages', async ({ packages, environment = 'global' }) => {
  try {
    const commands = {
      global: `pip install ${packages.join(' ')}`,
      user: `pip install --user ${packages.join(' ')}`,
      venv: `python -m venv venv && source venv/bin/activate && pip install ${packages.join(' ')}`
    };
    
    const command = commands[environment] || commands.global;
    return await ToolRegistry.execute('run_in_terminal', { command });
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Install Python packages dynamically', { category: 'terminal' });

ToolRegistry.register('configure_python_environment', async ({ envName = 'venv', requirements = [] }) => {
  try {
    const commands = [
      `python -m venv ${envName}`,
      os.platform() === 'win32' ? 
        `${envName}\\Scripts\\activate` : 
        `source ${envName}/bin/activate`
    ];
    
    if (requirements.length > 0) {
      commands.push(`pip install ${requirements.join(' ')}`);
    }
    
    const results = [];
    for (const command of commands) {
      const result = await ToolRegistry.execute('run_in_terminal', { command });
      results.push(result);
    }
    
    return { success: true, results, message: `Python environment ${envName} configured`, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Setup and manage Python virtual environments', { category: 'terminal' });

// ═══════════════════════════════════════════════════════════════════════════════
// 🌐 WEB & SEARCH TOOLS
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('fetch_webpage', async ({ url, extractText = true, timeout = 10000 }) => {
  try {
    const response = await axios.get(url, { 
      timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (extractText) {
      const $ = cheerio.load(response.data);
      
      // Remove script and style elements
      $('script, style, nav, header, footer, aside').remove();
      
      const title = $('title').text().trim();
      const content = $('body').text().replace(/\s+/g, ' ').trim();
      
      return {
        success: true,
        url,
        title,
        content: content.substring(0, 5000),
        length: content.length,
        statusCode: response.status,
        significant: true
      };
    }
    
    return {
      success: true,
      url,
      html: response.data.substring(0, 5000),
      statusCode: response.status,
      headers: response.headers
    };
  } catch (error) {
    return { success: false, error: error.message, url };
  }
}, 'Fetch and extract content from web pages', { category: 'web' });

ToolRegistry.register('semantic_web_search', async ({ query, limit = 5 }) => {
  try {
    // Using DuckDuckGo instant answer API
    const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
    
    const response = await axios.get(searchUrl, { timeout: 10000 });
    const data = response.data;
    
    const results = [];
    
    if (data.Answer) {
      results.push({
        title: 'Answer',
        url: data.AnswerURL || '',
        snippet: data.Answer,
        type: 'answer'
      });
    }
    
    if (data.RelatedTopics) {
      data.RelatedTopics.slice(0, limit).forEach(topic => {
        if (topic.Text && topic.FirstURL) {
          results.push({
            title: topic.Text.split(' - ')[0],
            url: topic.FirstURL,
            snippet: topic.Text,
            type: 'related'
          });
        }
      });
    }
    
    if (data.Abstract && results.length < limit) {
      results.push({
        title: data.Heading || 'Abstract',
        url: data.AbstractURL || '',
        snippet: data.Abstract,
        type: 'abstract'
      });
    }
    
    MemoryManager.addMemory('web_search', { query, results }, { importance: 2 });
    
    return { success: true, query, results, count: results.length, significant: true };
  } catch (error) {
    return { success: false, error: error.message, query };
  }
}, 'Natural language web search', { category: 'web' });

ToolRegistry.register('github_repo', async ({ query, type = 'repositories' }) => {
  try {
    const searchUrl = `https://api.github.com/search/${type}?q=${encodeURIComponent(query)}`;
    
    const response = await axios.get(searchUrl, { 
      timeout: 10000,
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'SHAI-Agent'
      }
    });
    
    const results = response.data.items.slice(0, 10).map(item => ({
      name: item.name || item.title,
      url: item.html_url,
      description: item.description,
      stars: item.stargazers_count,
      language: item.language,
      updated: item.updated_at
    }));
    
    return { success: true, query, results, count: results.length, significant: true };
  } catch (error) {
    return { success: false, error: error.message, query };
  }
}, 'Search GitHub repositories and snippets', { category: 'web' });

ToolRegistry.register('retrieval_augmented_generation', async ({ sources, query }) => {
  try {
    const extractedContent = [];
    
    // Extract content from multiple sources
    for (const source of sources) {
      if (source.startsWith('http')) {
        const { content } = await ToolRegistry.execute('fetch_webpage', { url: source });
        extractedContent.push({ source, content: content.substring(0, 2000) });
      } else if (fsSync.existsSync(source)) {
        const { content } = await ToolRegistry.execute('read_file', { filePath: source });
        extractedContent.push({ source, content: content.substring(0, 2000) });
      }
    }
    
    // Combine content for RAG processing
    const combinedContent = extractedContent.map(item => 
      `Source: ${item.source}\nContent: ${item.content}`
    ).join('\n\n');
    
    // Generate response using AI with RAG
    const prompt = `Based on the following sources, answer this query: "${query}"\n\nSources:\n${combinedContent}`;
    
    const result = await model.generateContent(prompt);
    const response = result.response.text();
    
    return { 
      success: true, 
      query, 
      sources: extractedContent.map(item => item.source),
      response,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Extract and summarize content from multiple sources', { category: 'web' });

// ═══════════════════════════════════════════════════════════════════════════════
// 🧪 TESTING & DEBUGGING TOOLS
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('run_tests', async ({ testPath = '.', framework = 'auto' }) => {
  try {
    const frameworks = {
      jest: 'jest',
      mocha: 'mocha',
      vitest: 'vitest run',
      pytest: 'pytest',
      go: 'go test',
      cargo: 'cargo test'
    };
    
    let command;
    
    if (framework === 'auto') {
      // Auto-detect test framework
      try {
        const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
        if (packageJson.devDependencies?.jest) command = 'jest';
        else if (packageJson.devDependencies?.vitest) command = 'vitest run';
        else if (packageJson.devDependencies?.mocha) command = 'mocha';
        else command = 'npm test';
      } catch {
        // Try common patterns
        if (fsSync.existsSync('pytest.ini') || fsSync.existsSync('setup.py')) command = 'pytest';
        else if (fsSync.existsSync('go.mod')) command = 'go test ./...';
        else if (fsSync.existsSync('Cargo.toml')) command = 'cargo test';
        else command = 'npm test';
      }
    } else {
      command = frameworks[framework] || framework;
    }
    
    return await ToolRegistry.execute('run_in_terminal', { 
      command: `${command} ${testPath}`,
      timeout: 60000 
    });
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Run tests with auto-framework detection', { category: 'testing' });

ToolRegistry.register('test_search', async ({ sourceFile, testDirectory = 'test' }) => {
  try {
    const baseName = path.basename(sourceFile, path.extname(sourceFile));
    const testPatterns = [
      `${baseName}.test.*`,
      `${baseName}.spec.*`,
      `test_${baseName}.*`,
      `*${baseName}*test*`
    ];
    
    const testFiles = [];
    
    for (const pattern of testPatterns) {
      const { files } = await ToolRegistry.execute('file_search', { 
        pattern, 
        directory: testDirectory 
      });
      testFiles.push(...files);
    }
    
    return { success: true, sourceFile, testFiles, count: testFiles.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Find tests related to source files', { category: 'testing' });

ToolRegistry.register('autonomous_debugger', async ({ filePath, errorDescription }) => {
  try {
    // Read file content
    const { content } = await ToolRegistry.execute('read_file', { filePath });
    
    // Analyze with AI for potential issues
    const debugPrompt = `Analyze this code for potential bugs related to: ${errorDescription}\n\nCode:\n${content}\n\nProvide specific suggestions for fixes.`;
    
    const result = await model.generateContent(debugPrompt);
    const analysis = result.response.text();
    
    // Get lint errors if available
    const { errors } = await ToolRegistry.execute('get_errors', { directory: path.dirname(filePath) });
    
    return { 
      success: true, 
      filePath, 
      analysis, 
      lintErrors: errors,
      suggestions: analysis,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Trace, identify and fix bugs automatically', { category: 'testing' });

ToolRegistry.register('self_repair', async ({ errors, context }) => {
  try {
    const repairPrompt = `Given these errors and context, suggest specific code fixes:\n\nErrors:\n${JSON.stringify(errors, null, 2)}\n\nContext:\n${context}`;
    
    const result = await model.generateContent(repairPrompt);
    const suggestions = result.response.text();
    
    return { 
      success: true, 
      errors, 
      suggestions,
      autoRepair: true,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'AI-suggested code fixes based on errors', { category: 'testing' });

// ═══════════════════════════════════════════════════════════════════════════════
// 🧠 AI & REASONING TOOLS  
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('natural_language_to_code', async ({ description, language = 'javascript', style = 'modern' }) => {
  try {
    const prompt = `Generate ${language} code based on this description: "${description}". 
    Use ${style} coding practices and include comments. 
    Only return the code, no explanations.`;
    
    const result = await model.generateContent(prompt);
    const code = result.response.text();
    
    return { 
      success: true, 
      code: code.replace(/```[\w]*\n?/g, '').trim(), 
      language, 
      description,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Convert natural language descriptions to code', { category: 'ai' });

ToolRegistry.register('intent_recognition', async ({ input }) => {
  try {
    const intentPrompt = `Analyze this user input and identify the intent, entities, and required actions: "${input}"
    
    Respond with JSON:
    {
      "intent": "create_file|edit_code|run_command|search|analyze|etc",
      "confidence": 0.0-1.0,
      "entities": ["entity1", "entity2"],
      "requiredTools": ["tool1", "tool2"],
      "parameters": {}
    }`;
    
    const result = await model.generateContent(intentPrompt);
    const response = result.response.text();
    
    try {
      const intent = JSON.parse(response.match(/\{[\s\S]*\}/)[0]);
      return { success: true, input, intent, significant: true };
    } catch (parseError) {
      return { success: true, input, intent: { intent: 'unknown', confidence: 0.5 } };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Understand what user wants from natural language', { category: 'ai' });

ToolRegistry.register('chain_of_thought_reasoning', async ({ problem, steps = [] }) => {
  try {
    const reasoningPrompt = `Break down this problem into clear reasoning steps: "${problem}"
    
    Previous steps: ${steps.join(', ')}
    
    Provide the next logical step in the reasoning chain.`;
    
    const result = await model.generateContent(reasoningPrompt);
    const nextStep = result.response.text();
    
    const updatedSteps = [...steps, nextStep];
    
    return { 
      success: true, 
      problem, 
      steps: updatedSteps,
      currentStep: nextStep,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Break down complex problems into logical steps', { category: 'ai' });

ToolRegistry.register('semantic_search', async ({ query, directory = '.', fileTypes = ['js', 'ts', 'py', 'go', 'rs'] }) => {
  try {
    // Get relevant memories first
    const relevantMemories = MemoryManager.searchMemory(query, 5);
    
    // Search for files
    const { files } = await ToolRegistry.execute('file_search', { 
      pattern: '**/*',
      directory,
      fileTypes 
    });
    
    const results = [];
    
    // Analyze each file for relevance
    for (const file of files.slice(0, 10)) {
      try {
        const { content } = await ToolRegistry.execute('read_file', { filePath: file.path });
        
        // Simple semantic matching
        const queryWords = query.toLowerCase().split(' ');
        const contentLower = content.toLowerCase();
        const relevanceScore = queryWords.reduce((score, word) => {
          const matches = (contentLower.match(new RegExp(word, 'g')) || []).length;
          return score + matches;
        }, 0);
        
        if (relevanceScore > 0) {
          results.push({
            path: file.path,
            relevanceScore,
            preview: content.substring(0, 200) + '...'
          });
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    return { 
      success: true, 
      query, 
      results: results.slice(0, 5),
      memories: relevantMemories,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Natural language search across codebase', { category: 'ai' });

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 UTILITY & WORKFLOW TOOLS
// ═══════════════════════════════════════════════════════════════════════════════

ToolRegistry.register('get_project_setup_info', async ({ directory = '.' }) => {
  try {
    const info = {
      directory,
      language: 'unknown',
      framework: 'unknown',
      buildTool: 'unknown',
      packageManager: 'unknown',
      files: {},
      dependencies: {}
    };
    
    // Check for common files
    const commonFiles = [
      'package.json', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml',
      'requirements.txt', 'setup.py', 'Pipfile',
      'go.mod', 'go.sum',
      'Cargo.toml', 'Cargo.lock',
      'composer.json',
      'pom.xml', 'build.gradle'
    ];
    
    for (const file of commonFiles) {
      const filePath = path.join(directory, file);
      if (fsSync.existsSync(filePath)) {
        info.files[file] = true;
        
        // Parse specific files for more info
        if (file === 'package.json') {
          const pkg = JSON.parse(await fs.readFile(filePath, 'utf8'));
          info.language = 'javascript';
          info.dependencies = pkg.dependencies || {};
          info.devDependencies = pkg.devDependencies || {};
          
          // Detect framework
          if (pkg.dependencies?.react) info.framework = 'react';
          else if (pkg.dependencies?.vue) info.framework = 'vue';
          else if (pkg.dependencies?.angular) info.framework = 'angular';
          else if (pkg.dependencies?.express) info.framework = 'express';
          else if (pkg.dependencies?.next) info.framework = 'nextjs';
        }
      }
    }
    
    // Detect package manager
    if (info.files['yarn.lock']) info.packageManager = 'yarn';
    else if (info.files['pnpm-lock.yaml']) info.packageManager = 'pnpm';
    else if (info.files['package-lock.json']) info.packageManager = 'npm';
    
    // Detect language by file extensions
    const { items } = await ToolRegistry.execute('list_dir', { dirPath: directory });
    const extensions = items.map(item => path.extname(item.name)).filter(Boolean);
    
    if (extensions.includes('.py')) info.language = 'python';
    else if (extensions.includes('.go')) info.language = 'go';
    else if (extensions.includes('.rs')) info.language = 'rust';
    else if (extensions.includes('.java')) info.language = 'java';
    else if (extensions.includes('.php')) info.language = 'php';
    
    return { success: true, info, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Detect framework, language, tooling etc', { category: 'utility' });

ToolRegistry.register('context_tracking_memory', async ({ action, data }) => {
  try {
    const memoryItem = {
      action,
      data,
      context: MemoryManager.getContextSummary(),
      timestamp: Date.now()
    };
    
    const memoryId = MemoryManager.addMemory('context_tracking', memoryItem, { importance: 3 });
    
    return { success: true, memoryId, action, significant: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Track what user said, did, expects', { category: 'utility' });

ToolRegistry.register('smart_status_report', async ({ taskId }) => {
  try {
    const relevantMemories = MemoryManager.searchMemory(taskId || 'task', 10);
    const recentContext = MemoryManager.getRecentContext(5);
    
    const statusPrompt = `Based on recent context and memories, provide a smart status report:
    
    Recent Context: ${JSON.stringify(recentContext)}
    Relevant Memories: ${JSON.stringify(relevantMemories)}
    
    What is the current status and what should happen next?`;
    
    const result = await model.generateContent(statusPrompt);
    const status = result.response.text();
    
    return { 
      success: true, 
      status, 
      memories: relevantMemories.length,
      contextItems: recentContext.length,
      significant: true 
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}, 'Status report with next steps', { category: 'utility' });

// ═══════════════════════════════════════════════════════════════════════════════
// 🤖 ENHANCED STREAMING AI AGENT WITH SUB-AGENTS
// ═══════════════════════════════════════════════════════════════════════════════

class SubAgent {
  constructor(name, specialization) {
    this.name = name;
    this.specialization = specialization;
    this.id = uuidv4();
    this.tasks = [];
    this.status = 'idle';
  }

  async execute(task) {
    this.status = 'working';
    console.log(UI.subAgent(this.name, task.description));
    
    try {
      // Simulate sub-agent work with specialized tools
      const result = await this.processTask(task);
      this.status = 'completed';
      return result;
    } catch (error) {
      this.status = 'failed';
      throw error;
    }
  }

  async processTask(task) {
    // Sub-agent logic based on specialization
    const tools = ToolRegistry.getByCategory(this.specialization);
    
    // Execute relevant tools based on task
    const results = [];
    for (const tool of tools.slice(0, 3)) { // Limit tools per sub-agent
      try {
        const result = await ToolRegistry.execute(tool.name, task.params || {});
        results.push({ tool: tool.name, result });
      } catch (error) {
        results.push({ tool: tool.name, error: error.message });
      }
    }
    
    return {
      subAgent: this.name,
      specialization: this.specialization,
      task: task.description,
      results,
      status: 'completed'
    };
  }
}

class StreamingAgent {
  constructor() {
    this.isThinking = false;
    this.currentResponse = '';
    this.chainOfThought = [];
    this.subAgents = new Map();
    
    // Initialize specialized sub-agents
    this.initializeSubAgents();
  }

  initializeSubAgents() {
    const agents = [
      new SubAgent('FileSystem-Agent', 'filesystem'),
      new SubAgent('Terminal-Agent', 'terminal'),
      new SubAgent('Web-Agent', 'web'),
      new SubAgent('Testing-Agent', 'testing'),
      new SubAgent('AI-Agent', 'ai'),
      new SubAgent('Utility-Agent', 'utility')
    ];
    
    agents.forEach(agent => {
      this.subAgents.set(agent.specialization, agent);
    });
  }

  async initialize() {
    try {
      await MemoryManager.ensureDirectories();
      await MemoryManager.loadMemory();
      await MemoryManager.loadContext();
      
      genAI = new GoogleGenerativeAI(config.apiKey);
      model = genAI.getGenerativeModel({ 
        model: config.model,
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
        }
      });
      
      return true;
    } catch (error) {
      console.log(UI.error(`Initialization failed: ${error.message}`));
      return false;
    }
  }

  async processInput(input) {
    try {
      // Add user input to context
      MemoryManager.addContext('user', input);
      
      // Show thinking indicator
      console.log(UI.thinking('Analyzing your request...', 'analyzing'));
      
      // Recognize intent
      const { intent } = await ToolRegistry.execute('intent_recognition', { input });
      
      if (intent && intent.intent !== 'unknown') {
        console.log(UI.chainOfThought(1, `Identified intent: ${intent.intent} (confidence: ${intent.confidence})`));
        
        // Plan execution
        await this.planExecution(input, intent);
      } else {
        // Direct conversation mode
        await this.generateDirectResponse(input);
      }
      
    } catch (error) {
      console.log(UI.error(`Processing failed: ${error.message}`));
    }
  }

  async planExecution(input, intent) {
    console.log(UI.thinking('Planning execution strategy...', 'planning'));
    
    // Determine if sub-agents are needed
    const shouldUseSubAgents = intent.requiredTools && intent.requiredTools.length > 3;
    
    if (shouldUseSubAgents) {
      console.log(UI.chainOfThought(2, 'Complex task detected - delegating to sub-agents'));
      await this.delegateToSubAgents(input, intent);
    } else {
      console.log(UI.chainOfThought(2, 'Simple task - executing directly'));
      await this.executeDirectly(input, intent);
    }
  }

  async delegateToSubAgents(input, intent) {
    const tasks = [];
    
    // Group tools by category and create sub-agent tasks
    const toolsByCategory = {};
    
    if (intent.requiredTools) {
      intent.requiredTools.forEach(toolName => {
        const tool = ToolRegistry.tools.get(toolName);
        if (tool) {
          const category = tool.category;
          if (!toolsByCategory[category]) toolsByCategory[category] = [];
          toolsByCategory[category].push(toolName);
        }
      });
    }
    
    // Execute sub-agents in parallel
    const subAgentPromises = Object.entries(toolsByCategory).map(([category, tools]) => {
      const agent = this.subAgents.get(category);
      if (agent) {
        const task = {
          description: `Handle ${category} operations for: ${input}`,
          tools,
          params: intent.parameters || {}
        };
        return agent.execute(task);
      }
    }).filter(Boolean);
    
    const results = await Promise.all(subAgentPromises);
    
    // Generate final response based on sub-agent results
    await this.synthesizeResponse(input, results);
  }

  async executeDirectly(input, intent) {
    console.log(UI.thinking('Executing tools directly...', 'deciding'));
    
    const toolResults = [];
    
    if (intent.requiredTools) {
      for (const toolName of intent.requiredTools) {
        console.log(UI.chainOfThought(3, `Executing ${toolName}`));
        try {
          const result = await ToolRegistry.execute(toolName, intent.parameters || {});
          toolResults.push({ tool: toolName, result });
        } catch (error) {
          toolResults.push({ tool: toolName, error: error.message });
        }
      }
    }
    
    await this.generateFinalResponse(input, toolResults);
  }

  async synthesizeResponse(input, subAgentResults) {
    console.log(UI.thinking('Synthesizing sub-agent results...'));
    
    const synthesisPrompt = `
User request: "${input}"

Sub-agent results:
${JSON.stringify(subAgentResults, null, 2)}

Provide a comprehensive response that synthesizes all sub-agent results into a helpful answer.
Be conversational and focus on what was accomplished and any next steps.`;

    console.log(UI.response());
    
    const result = await model.generateContent(synthesisPrompt);
    const response = result.response.text();
    
    // Stream the response
    await this.streamText(response);
    
    // Add to context and memory
    MemoryManager.addContext('assistant', response, { subAgentResults });
    MemoryManager.addMemory('synthesized_response', { input, response, subAgentResults }, { importance: 3 });
  }

  async generateFinalResponse(input, toolResults) {
    const responsePrompt = `
User request: "${input}"

Tool results:
${JSON.stringify(toolResults, null, 2)}

Provide a helpful, conversational response based on the results. Be natural and informative.`;

    console.log(UI.response());
    
    const result = await model.generateContent(responsePrompt);
    const response = result.response.text();
    
    // Stream the response
    await this.streamText(response);
    
    // Add to context
    MemoryManager.addContext('assistant', response, { toolResults });
    
    // Store significant interactions
    if (toolResults.length > 0 || response.length > 200) {
      MemoryManager.addMemory('conversation', { input, response, tools: toolResults }, { importance: 2 });
    }
  }

  async generateDirectResponse(input) {
    console.log(UI.thinking('Generating direct response...'));
    
    // Get context and memories for better responses
    const recentContext = MemoryManager.getRecentContext(10);
    const relevantMemories = MemoryManager.searchMemory(input, 5);
    
    const conversationPrompt = `
User: ${input}

Recent context: ${JSON.stringify(recentContext)}
Relevant memories: ${JSON.stringify(relevantMemories)}

Respond naturally and helpfully. If the user needs tools, suggest which ones might be useful.`;

    console.log(UI.response());
    
    const result = await model.generateContent(conversationPrompt);
    const response = result.response.text();
    
    // Stream the response
    await this.streamText(response);
    
    // Add to context
    MemoryManager.addContext('assistant', response);
  }

  async streamText(text, delay = 15) {
    const words = text.split(' ');
    for (let i = 0; i < words.length; i++) {
      process.stdout.write(words[i] + ' ');
      
      // Add slight delay for natural streaming effect
      if (i % 3 === 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    console.log('\n');
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🚀 ENHANCED CLI INTERFACE
// ═══════════════════════════════════════════════════════════════════════════════

async function startInteractiveMode() {
  UI.banner();
  
  const agent = new StreamingAgent();
  
  console.log(UI.colors.muted('🔧 Initializing SHAI Agent v4.0...'));
  const initialized = await agent.initialize();
  
  if (!initialized) {
    console.log(UI.error('Failed to initialize. Please check your API key.'));
    process.exit(1);
  }
  
  console.log(UI.success('🚀 Agent ready! Type your request or "help" for commands.'));
  console.log(UI.colors.dimmed(`💾 Memory: ${memory.length} items | 🧠 Context: ${context.length} items | ⚡ Tools: ${ToolRegistry.tools.size} available`));
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: UI.prompt()
  });

  rl.prompt();

  rl.on('line', async (input) => {
    const trimmedInput = input.trim();
    
    if (!trimmedInput) {
      rl.prompt();
      return;
    }
    
    // Handle special commands
    switch (trimmedInput.toLowerCase()) {
      case 'exit':
      case 'quit':
        console.log(UI.colors.secondary('🫡 Goodbye! Thanks for using SHAI Agent v4.0'));
        process.exit(0);
        break;
        
      case 'help':
        displayHelp();
        rl.prompt();
        return;
        
      case 'tools':
        displayTools();
        rl.prompt();
        return;
        
      case 'memory':
        displayMemory();
        rl.prompt();
        return;
        
      case 'context':
        displayContext();
        rl.prompt();
        return;
        
      case 'agents':
        displaySubAgents();
        rl.prompt();
        return;
        
      case 'clear':
        console.clear();
        UI.banner();
        rl.prompt();
        return;
        
      case 'stats':
        displayStats();
        rl.prompt();
        return;
        
      default:
        await agent.processInput(trimmedInput);
    }
    
    rl.prompt();
  });

  rl.on('close', () => {
    console.log(UI.colors.secondary('\n🫡 Goodbye! Thanks for using SHAI Agent v4.0'));
    process.exit(0);
  });
}

function displayHelp() {
  console.log(UI.section('Available Commands'));
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('help')}      - Show this help message`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('tools')}     - List all available tools`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('memory')}    - Show stored memories`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('context')}   - Show conversation context`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('agents')}    - Show sub-agent status`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('stats')}     - Show usage statistics`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('clear')}     - Clear screen`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.code('exit')}      - Exit the agent`);
  console.log(`${UI.colors.primary('│')}`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.secondary('Example requests:')}`);
  console.log(`${UI.colors.primary('│')} "Create a React component for user authentication"`);
  console.log(`${UI.colors.primary('│')} "Search for all TODO comments in my codebase"`);
  console.log(`${UI.colors.primary('│')} "Run tests and fix any failures you find"`);
  console.log(`${UI.colors.primary('│')} "Analyze my main.py file for performance issues"`);
  console.log(`${UI.colors.primary('│')} "Search GitHub for machine learning libraries"`);
}

function displayTools() {
  console.log(UI.section('Available Tools (40+)'));
  const toolsByCategory = {};
  
  ToolRegistry.list().forEach(tool => {
    const category = tool.category || 'general';
    if (!toolsByCategory[category]) toolsByCategory[category] = [];
    toolsByCategory[category].push(tool);
  });
  
  Object.entries(toolsByCategory).forEach(([category, tools]) => {
    console.log(`${UI.colors.primary('│')} ${UI.colors.secondary.bold(category.toUpperCase())} (${tools.length} tools)`);
    tools.forEach(tool => {
      const usage = tool.usage > 0 ? UI.colors.dimmed(` (used ${tool.usage}x)`) : '';
      console.log(`${UI.colors.primary('│')}   ${UI.colors.code(tool.name)} - ${UI.colors.muted(tool.description)}${usage}`);
    });
    console.log(`${UI.colors.primary('│')}`);
  });
}

function displayMemory() {
  console.log(UI.section('Memory Bank'));
  const recentMemories = MemoryManager.searchMemory('', 15);
  
  if (recentMemories.length === 0) {
    console.log(`${UI.colors.primary('│')} ${UI.colors.muted('No memories stored yet')}`);
  } else {
    console.log(`${UI.colors.primary('│')} ${UI.colors.dimmed(`Total: ${memory.length} memories`)}`);
    recentMemories.forEach((mem, i) => {
      const date = new Date(mem.timestamp).toLocaleString();
      const content = typeof mem.content === 'string' ? mem.content : JSON.stringify(mem.content);
      console.log(`${UI.colors.primary('│')} ${UI.colors.secondary(`${i+1}.`)} ${UI.colors.highlight(mem.type)} - ${content.substring(0, 60)}...`);
      console.log(`${UI.colors.primary('│')}    ${UI.colors.dimmed(`${date} | Importance: ${mem.importance}`)}`);
    });
  }
  console.log(`${UI.colors.primary('│')}`);
}

function displayContext() {
  console.log(UI.section('Conversation Context'));
  const recentContext = MemoryManager.getRecentContext(10);
  
  if (recentContext.length === 0) {
    console.log(`${UI.colors.primary('│')} ${UI.colors.muted('No context available')}`);
  } else {
    recentContext.forEach((ctx, i) => {
      const date = new Date(ctx.timestamp).toLocaleString();
      const content = typeof ctx.content === 'string' ? ctx.content : JSON.stringify(ctx.content);
      const roleColor = ctx.role === 'user' ? UI.colors.secondary : UI.colors.highlight;
      console.log(`${UI.colors.primary('│')} ${roleColor(ctx.role)}: ${content.substring(0, 80)}...`);
      console.log(`${UI.colors.primary('│')}    ${UI.colors.dimmed(date)}`);
    });
  }
  console.log(`${UI.colors.primary('│')}`);
}

function displaySubAgents() {
  console.log(UI.section('Sub-Agent Network'));
  const agent = new StreamingAgent();
  
  agent.subAgents.forEach((subAgent, category) => {
    const statusColor = subAgent.status === 'idle' ? UI.colors.muted : 
                       subAgent.status === 'working' ? UI.colors.warning :
                       subAgent.status === 'completed' ? UI.colors.success : UI.colors.error;
    
    console.log(`${UI.colors.primary('│')} ${UI.colors.code(subAgent.name)} - ${statusColor(subAgent.status)}`);
    console.log(`${UI.colors.primary('│')}   Specialization: ${UI.colors.secondary(subAgent.specialization)}`);
    console.log(`${UI.colors.primary('│')}   Tasks completed: ${subAgent.tasks.length}`);
  });
  console.log(`${UI.colors.primary('│')}`);
}

function displayStats() {
  console.log(UI.section('Usage Statistics'));
  
  const toolStats = ToolRegistry.list()
    .filter(tool => tool.usage > 0)
    .sort((a, b) => b.usage - a.usage)
    .slice(0, 10);
  
  console.log(`${UI.colors.primary('│')} ${UI.colors.secondary('Most Used Tools:')}`);
  toolStats.forEach((tool, i) => {
    console.log(`${UI.colors.primary('│')}   ${i+1}. ${UI.colors.code(tool.name)} - ${UI.colors.highlight(tool.usage)} uses`);
  });
  
  console.log(`${UI.colors.primary('│')}`);
  console.log(`${UI.colors.primary('│')} ${UI.colors.secondary('System Stats:')}`);
  console.log(`${UI.colors.primary('│')}   Memory entries: ${UI.colors.highlight(memory.length)}`);
  console.log(`${UI.colors.primary('│')}   Context entries: ${UI.colors.highlight(context.length)}`);
  console.log(`${UI.colors.primary('│')}   Available tools: ${UI.colors.highlight(ToolRegistry.tools.size)}`);
  console.log(`${UI.colors.primary('│')}   Sub-agents: ${UI.colors.highlight('6')}`);
  console.log(`${UI.colors.primary('│')}`);
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 COMMAND LINE PROGRAM SETUP
// ═══════════════════════════════════════════════════════════════════════════════

program
  .name('shai')
  .description('SHAI - Smart AI Agent v4.0 with 40+ tools and advanced reasoning')
  .version('4.0.0');

program
  .command('chat')
  .description('Start interactive chat mode')
  .action(startInteractiveMode);

program
  .command('run')
  .description('Execute a single command')
  .argument('<prompt>', 'Command to execute')
  .action(async (prompt) => {
    const agent = new StreamingAgent();
    const initialized = await agent.initialize();
    
    if (!initialized) {
      console.log(UI.error('Failed to initialize agent'));
      process.exit(1);
    }
    
    await agent.processInput(prompt);
  });

program
  .command('tools')
  .description('List all available tools')
  .action(() => {
    displayTools();
  });

program
  .command('memory')
  .description('Show stored memories')
  .action(() => {
    displayMemory();
  });

program
  .command('stats')
  .description('Show usage statistics')
  .action(() => {
    displayStats();
  });

// ═══════════════════════════════════════════════════════════════════════════════
// 🚀 STARTUP & EXPORTS
// ═══════════════════════════════════════════════════════════════════════════════

if (process.argv.length === 2) {
  // No arguments provided, start interactive mode
  startInteractiveMode();
} else {
  // Parse command line arguments
  program.parse();
}

// Export for programmatic use
module.exports = {
  StreamingAgent,
  SubAgent,
  ToolRegistry,
  MemoryManager,
  UI
};
