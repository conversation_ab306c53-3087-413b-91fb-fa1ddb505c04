#!/usr/bin/env node

// Simple test script to isolate the SHAI agent issues

const { StreamingAgent, ToolRegistry, MemoryManager, UI } = require('./agent.js');

async function testBasicFunctionality() {
  console.log('🧪 Testing SHAI Agent v5.0 ULTRA Basic Functionality\n');
  
  try {
    // Test 1: Tool Registry
    console.log('1. Testing Tool Registry...');
    const toolCount = ToolRegistry.tools.size;
    console.log(`   ✅ ${toolCount} tools registered`);
    
    // Test 2: Simple tool execution
    console.log('2. Testing simple tool execution...');
    try {
      const result = await ToolRegistry.execute('list_dir', { dirPath: '.', recursive: false });
      if (result.success) {
        console.log(`   ✅ list_dir tool executed successfully`);
        console.log(`   📁 Found ${result.items?.length || 0} items`);
      } else {
        console.log(`   ❌ list_dir tool failed: ${result.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Tool execution error: ${error.message}`);
    }
    
    // Test 3: Intent recognition
    console.log('3. Testing intent recognition...');
    const testInputs = [
      'list all files in the current directory',
      'read the package.json file',
      'create a hello world file',
      'run the command ls'
    ];

    for (const testInput of testInputs) {
      try {
        console.log(`   Testing: "${testInput}"`);
        const intentResult = await ToolRegistry.execute('intent_recognition', { input: testInput });
        if (intentResult.success) {
          console.log(`   ✅ Intent: ${intentResult.intent.intent} (confidence: ${intentResult.intent.confidence})`);
          console.log(`   🛠️ Tools: ${intentResult.intent.requiredTools?.join(', ') || 'none'}`);
        } else {
          console.log(`   ❌ Failed: ${intentResult.error}`);
        }
        console.log('');
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
      }
    }
    
    // Test 4: Agent initialization
    console.log('4. Testing agent initialization...');
    try {
      const agent = new StreamingAgent();
      const initialized = await agent.initialize();
      if (initialized) {
        console.log('   ✅ Agent initialized successfully');
      } else {
        console.log('   ❌ Agent initialization failed');
      }
    } catch (error) {
      console.log(`   ❌ Agent initialization error: ${error.message}`);
    }
    
    // Test 5: Predictive analysis
    console.log('5. Testing predictive analysis...');
    try {
      const agent = new StreamingAgent();
      await agent.initialize();

      console.log('   🔄 Testing predictive engine...');

      // Test the predictive analysis that might be causing the hang
      const testInput = 'list all files in the current directory';

      console.log('   📊 Running predictive analysis...');
      const predictiveAnalysis = await agent.predictiveEngine.analyzePredictiveContext(testInput, {
        workingDirectory: process.cwd(),
        recentContext: []
      });

      console.log(`   ✅ Predictive analysis completed - Complexity: ${predictiveAnalysis.complexity.level}`);
      console.log(`   🎯 Intent: ${predictiveAnalysis.intent.primary} (${predictiveAnalysis.intent.confidence})`);
      console.log(`   🛠️ Required tools: ${predictiveAnalysis.requiredTools.map(t => t.tool).join(', ')}`);

    } catch (error) {
      console.log(`   ❌ Predictive analysis error: ${error.message}`);
      console.log(`   📋 Stack: ${error.stack}`);
    }

    // Test 6: Full agent processing (with timeout)
    console.log('6. Testing full agent processing with timeout...');
    try {
      const agent = new StreamingAgent();
      await agent.initialize();

      console.log('   🔄 Processing full input with timeout...');

      // Set a timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Processing timeout after 10 seconds')), 10000);
      });

      const processingPromise = agent.processInput('list all files in the current directory');

      await Promise.race([processingPromise, timeoutPromise]);

      console.log('   ✅ Full processing completed successfully');

    } catch (error) {
      if (error.message.includes('timeout')) {
        console.log('   ⚠️ Processing timed out (this indicates a hanging issue)');
      } else {
        console.log(`   ❌ Full processing error: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Basic functionality test completed!');
    
  } catch (error) {
    console.log(`\n💥 Test failed with error: ${error.message}`);
    console.log(error.stack);
  }
}

// Run the test
testBasicFunctionality().then(() => {
  console.log('\n✨ Test script finished');
  process.exit(0);
}).catch(error => {
  console.error('\n💥 Test script failed:', error.message);
  process.exit(1);
});
